# Android 打包腳本使用指南

本專案提供了兩個主要的 Android 打包腳本，支援 APK 和 AAB 格式的建置與上傳。

## 📱 腳本概覽

### 1. `android_build.sh` - 綜合打包腳本
- 支援 APK 和 AAB 建置
- 自動上傳 APK 到 Firebase App Distribution
- 可選上傳 AAB 到 Google Play Console

### 2. `aab_upload.sh` - 專用 AAB 上傳腳本
- 專門用於建置和上傳 AAB 到 Google Play Console
- 支援不同發布軌道選擇
- 簡化的 AAB 上傳流程

## 🚀 使用方式

### 基本 APK 建置

```bash
# 建置 Release APK 並上傳到 Firebase
./android_build.sh release apk

# 建置 Debug APK
./android_build.sh debug apk
```

### AAB 建置與上傳

```bash
# 建置 AAB（不上傳）
./android_build.sh release aab

# 建置 AAB 並上傳到 Google Play Console 內部測試軌道
./android_build.sh release aab --upload-play

# 同時建置 APK 和 AAB
./android_build.sh release both

# 建置 APK 和 AAB，並分別上傳到 Firebase 和 Google Play Console
./android_build.sh release both --upload-play
```

### 專用 AAB 上傳腳本

```bash
# 上傳到內部測試軌道（預設）
./aab_upload.sh

# 上傳到不同軌道
./aab_upload.sh internal   # 內部測試
./aab_upload.sh alpha      # Alpha 測試
./aab_upload.sh beta       # Beta 測試
./aab_upload.sh production # 正式發布（需確認）
```

## ⚙️ 設定需求

### Firebase App Distribution 設定
- 已在腳本中預設配置
- APP_ID: `1:************:android:4971c9e15686127296aa1f`
- 測試者: `<EMAIL>`

### Google Play Console 設定
1. **服務帳戶金鑰**
   ```bash
   # 將服務帳戶 JSON 檔案放置到正確位置
   cp your-service-account-key.json android/play-console-service-account.json
   ```

2. **簽名設定**
   - 確保 `android/key.properties` 存在且設定正確
   - 確保 `astreal-key.jks` 金鑰檔案存在

3. **Gradle 插件**
   - 已在 `android/app/build.gradle.kts` 中配置 Play Publisher 插件

## 📋 建置輸出

### APK 檔案
- 路徑: `build/app/outputs/flutter-apk/`
- 命名格式: `Astreal_[release|debug]_v[版本]_[建置號].apk`

### AAB 檔案
- 路徑: `build/app/outputs/bundle/release/`
- 命名格式: `Astreal_release_v[版本]_[建置號]_[軌道].aab`

## 🔧 版本管理

腳本會自動從 `pubspec.yaml` 讀取版本資訊：
```yaml
version: 1.0.0+1
#        ^版本號  ^建置號
```

## 📝 發布說明

腳本會自動產生發布說明：
- 取得最近 30 筆 Git commit 訊息
- 寫入 `RELEASE_NOTE.txt` 檔案
- 用於 Firebase App Distribution 和 Google Play Console

## ⚠️ 注意事項

1. **版本號管理**
   - 每次發布前請更新 `pubspec.yaml` 中的版本號
   - Google Play Console 要求建置號必須遞增

2. **網路需求**
   - 上傳過程需要穩定的網路連線
   - Firebase 和 Google Play Console 上傳可能需要較長時間

3. **權限檢查**
   - 確保 Firebase 專案權限正確
   - 確保 Google Play Console 服務帳戶權限充足

4. **安全性**
   - 服務帳戶金鑰檔案已加入 `.gitignore`
   - 請勿將敏感檔案提交到版本控制

## 🛠 故障排除

### 常見問題

1. **Flutter 未找到**
   ```bash
   export PATH="/Users/<USER>/development/flutter/bin:$PATH"
   ```

2. **Firebase CLI 未安裝**
   ```bash
   npm install -g firebase-tools
   firebase login
   ```

3. **簽名錯誤**
   - 檢查 `android/key.properties` 設定
   - 確認金鑰檔案路徑正確

4. **Google Play Console 上傳失敗**
   - 檢查服務帳戶權限
   - 確認版本號大於目前發布版本
   - 檢查網路連線

### 除錯指令

```bash
# 檢查 Flutter 環境
flutter doctor

# 檢查 Gradle 任務
cd android && ./gradlew tasks --all | grep publish

# 詳細錯誤訊息
cd android && ./gradlew publishReleaseBundle --info --stacktrace
```

## 📚 相關文件

- [AAB上傳Google Play Console設定指南.md](./AAB上傳Google%20Play%20Console設定指南.md)
- [打包版本.md](./打包版本.md)
- [打包腳本.md](./打包腳本.md)

## 🎯 快速開始

1. **首次設定**
   ```bash
   # 設定 Google Play Console 服務帳戶
   cp your-service-account.json android/play-console-service-account.json
   
   # 確認簽名設定
   cat android/key.properties
   ```

2. **日常建置**
   ```bash
   # 開發測試：建置 APK 上傳到 Firebase
   ./android_build.sh release apk
   
   # 正式發布：建置 AAB 上傳到 Google Play Console
   ./aab_upload.sh internal
   ```

3. **版本發布流程**
   ```bash
   # 1. 更新版本號
   # 編輯 pubspec.yaml
   
   # 2. 提交變更
   git add . && git commit -m "Release v1.0.1"
   
   # 3. 建置並上傳
   ./android_build.sh release both --upload-play
   ```
