#!/bin/bash
# 測試建置環境設定腳本
# 使用方式: ./test_build_setup.sh

# 顏色定義
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo -e "${BLUE}=== AstReal 建置環境檢查 ===${NC}"
echo ""

# 檢查 Flutter
echo -e "${YELLOW}檢查 Flutter...${NC}"
if command -v flutter >/dev/null 2>&1; then
    FLUTTER_VERSION=$(flutter --version | head -n 1)
    echo -e "${GREEN}✅ Flutter 已安裝: $FLUTTER_VERSION${NC}"
else
    echo -e "${RED}❌ Flutter 未安裝或未加入 PATH${NC}"
fi

# 檢查 Firebase CLI
echo -e "${YELLOW}檢查 Firebase CLI...${NC}"
if command -v firebase >/dev/null 2>&1; then
    FIREBASE_VERSION=$(firebase --version)
    echo -e "${GREEN}✅ Firebase CLI 已安裝: $FIREBASE_VERSION${NC}"
else
    echo -e "${RED}❌ Firebase CLI 未安裝${NC}"
    echo -e "${YELLOW}安裝指令: npm install -g firebase-tools${NC}"
fi

# 檢查 Android 簽名設定
echo -e "${YELLOW}檢查 Android 簽名設定...${NC}"
if [ -f "android/key.properties" ]; then
    echo -e "${GREEN}✅ key.properties 存在${NC}"
    
    # 檢查金鑰檔案
    KEYSTORE_FILE=$(grep "storeFile=" android/key.properties | cut -d'=' -f2)
    if [ -f "$KEYSTORE_FILE" ] || [ -f "android/$KEYSTORE_FILE" ]; then
        echo -e "${GREEN}✅ 金鑰檔案存在${NC}"
    else
        echo -e "${RED}❌ 金鑰檔案不存在: $KEYSTORE_FILE${NC}"
    fi
else
    echo -e "${RED}❌ android/key.properties 不存在${NC}"
fi

# 檢查 Google Play Console 設定
echo -e "${YELLOW}檢查 Google Play Console 設定...${NC}"
if [ -f "android/play-console-service-account.json" ]; then
    echo -e "${GREEN}✅ 服務帳戶金鑰存在${NC}"
else
    echo -e "${YELLOW}⚠️  服務帳戶金鑰不存在（AAB 上傳功能將無法使用）${NC}"
    echo -e "${YELLOW}請參考: 文件/打包/AAB上傳Google Play Console設定指南.md${NC}"
fi

# 檢查 Gradle Play Publisher 插件
echo -e "${YELLOW}檢查 Gradle Play Publisher 插件...${NC}"
if grep -q "com.github.triplet.play" android/app/build.gradle.kts; then
    echo -e "${GREEN}✅ Play Publisher 插件已配置${NC}"
else
    echo -e "${RED}❌ Play Publisher 插件未配置${NC}"
fi

# 檢查版本設定
echo -e "${YELLOW}檢查版本設定...${NC}"
if [ -f "pubspec.yaml" ]; then
    VERSION=$(grep '^version:' pubspec.yaml | sed 's/version: //')
    if [ -n "$VERSION" ]; then
        echo -e "${GREEN}✅ 版本設定: $VERSION${NC}"
    else
        echo -e "${RED}❌ 無法讀取版本號${NC}"
    fi
else
    echo -e "${RED}❌ pubspec.yaml 不存在${NC}"
fi

# 檢查腳本權限
echo -e "${YELLOW}檢查腳本權限...${NC}"
if [ -x "android_build.sh" ]; then
    echo -e "${GREEN}✅ android_build.sh 可執行${NC}"
else
    echo -e "${YELLOW}⚠️  android_build.sh 無執行權限${NC}"
    echo -e "${YELLOW}修正指令: chmod +x android_build.sh${NC}"
fi

if [ -x "aab_upload.sh" ]; then
    echo -e "${GREEN}✅ aab_upload.sh 可執行${NC}"
else
    echo -e "${YELLOW}⚠️  aab_upload.sh 無執行權限${NC}"
    echo -e "${YELLOW}修正指令: chmod +x aab_upload.sh${NC}"
fi

# 檢查 Flutter 專案
echo -e "${YELLOW}檢查 Flutter 專案...${NC}"
if flutter doctor >/dev/null 2>&1; then
    echo -e "${GREEN}✅ Flutter 專案環境正常${NC}"
else
    echo -e "${RED}❌ Flutter 專案環境有問題${NC}"
    echo -e "${YELLOW}請執行: flutter doctor${NC}"
fi

echo ""
echo -e "${BLUE}=== 檢查完成 ===${NC}"
echo ""
echo -e "${YELLOW}建議的下一步：${NC}"
echo "1. 如有錯誤，請根據上述提示修正"
echo "2. 執行 flutter doctor 檢查完整環境"
echo "3. 測試建置: ./android_build.sh debug apk"
echo "4. 參考文件: 文件/打包/Android打包腳本使用指南.md"
