#!/bin/bash
# AAB 建置並上傳到 Google Play Console 腳本
# 使用方式: ./aab_upload.sh [internal|alpha|beta|production]
# 範例: ./aab_upload.sh internal

set -e  # 遇到錯誤時停止執行

# 顏色定義
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 設定 Flutter PATH
export PATH="/Users/<USER>/development/flutter/bin:$PATH"

# 參數解析
TRACK="internal"
if [ $# -gt 0 ]; then
    case $1 in
        internal|alpha|beta|production)
            TRACK="$1"
            ;;
        *)
            echo -e "${RED}未知發布軌道: $1${NC}"
            echo "使用方式: $0 [internal|alpha|beta|production]"
            exit 1
            ;;
    esac
fi

echo -e "${BLUE}=== AstReal AAB 上傳腳本 ===${NC}"
echo -e "${YELLOW}發布軌道：${NC}$TRACK"
echo ""

# 防呆檢查
command -v flutter >/dev/null 2>&1 || { echo -e "${RED}❌ Flutter 未安裝${NC}"; exit 1; }

# 檢查服務帳戶金鑰
if [ ! -f "android/play-console-service-account.json" ]; then
    echo -e "${RED}❌ 找不到 Google Play Console 服務帳戶金鑰檔案${NC}"
    echo -e "${YELLOW}請將服務帳戶 JSON 檔案放置於: android/play-console-service-account.json${NC}"
    echo -e "${YELLOW}參考設定指南: 文件/打包/AAB上傳Google Play Console設定指南.md${NC}"
    exit 1
fi

# 檢查簽名設定
if [ ! -f "android/key.properties" ]; then
    echo -e "${RED}❌ 找不到簽名設定檔案 android/key.properties${NC}"
    exit 1
fi

# 生成時間戳
TIMESTAMP=$(date +"%Y%m%d_%H%M%S")
COMPILATION_TIMESTAMP=$(date +"%Y%m%d%H%M%S")
BUILD_DATE=$(date +"%Y-%m-%d %H:%M:%S")

echo -e "${YELLOW}建置時間：${NC}$BUILD_DATE"
echo ""

# 從 pubspec.yaml 讀取版本號
VERSION=$(grep '^version:' pubspec.yaml | sed 's/version: //' | sed 's/+.*//')
BUILD_NUMBER=$(grep '^version:' pubspec.yaml | sed 's/version: //' | cut -d'+' -f2)

if [ -z "$VERSION" ]; then
    echo -e "${RED}錯誤：無法從 pubspec.yaml 讀取版本號${NC}"
    exit 1
fi

echo -e "${YELLOW}應用版本：${NC}$VERSION"
echo -e "${YELLOW}建置號碼：${NC}$BUILD_NUMBER"
echo ""

# 取得最新 Git commit messages
echo -e "${BLUE}📝 取得 Git 提交記錄...${NC}"
GIT_MESSAGES=$(git log -10 --pretty=format:"- %s")

# 寫入到 RELEASE_NOTE 檔案
echo "Release Note for $TRACK track:" > RELEASE_NOTE.txt
echo "$GIT_MESSAGES" >> RELEASE_NOTE.txt

# 建置 AAB
echo -e "${BLUE}📦 建置 Android App Bundle (AAB)...${NC}"
flutter build appbundle --release \
  --build-name=$VERSION \
  --build-number=$BUILD_NUMBER \
  --dart-define=COMPILATION_TIMESTAMP=$COMPILATION_TIMESTAMP

# 檢查 AAB 是否存在
AAB_PATH="build/app/outputs/bundle/release/app-release.aab"
if [ ! -f "$AAB_PATH" ]; then
    echo -e "${RED}❌ 找不到 AAB 檔案，建置可能失敗${NC}"
    exit 1
fi

# 重新命名 AAB
AAB_NAME="Astreal_release_v${VERSION}_${BUILD_NUMBER}_${TRACK}.aab"
NEW_AAB_PATH="build/app/outputs/bundle/release/${AAB_NAME}"
cp "$AAB_PATH" "$NEW_AAB_PATH"

echo -e "${YELLOW}AAB 檔案：${NC}$AAB_NAME"
echo ""

# 上傳到 Google Play Console
echo -e "${BLUE}🚀 上傳 AAB 到 Google Play Console ($TRACK 軌道)...${NC}"

cd android

# 設定服務帳戶金鑰路徑
export ANDROID_PUBLISHER_CREDENTIALS="play-console-service-account.json"

# 根據軌道選擇上傳指令
case $TRACK in
    internal)
        echo -e "${YELLOW}上傳到內部測試軌道...${NC}"
        ./gradlew publishReleaseBundle --track=internal
        ;;
    alpha)
        echo -e "${YELLOW}上傳到 Alpha 測試軌道...${NC}"
        ./gradlew publishReleaseBundle --track=alpha
        ;;
    beta)
        echo -e "${YELLOW}上傳到 Beta 測試軌道...${NC}"
        ./gradlew publishReleaseBundle --track=beta
        ;;
    production)
        echo -e "${YELLOW}上傳到正式發布軌道...${NC}"
        echo -e "${RED}⚠️  警告：這將發布到正式環境！${NC}"
        read -p "確定要繼續嗎？(y/N): " -n 1 -r
        echo
        if [[ $REPLY =~ ^[Yy]$ ]]; then
            ./gradlew publishReleaseBundle --track=production
        else
            echo -e "${YELLOW}已取消上傳${NC}"
            cd ..
            exit 0
        fi
        ;;
esac

if [ $? -eq 0 ]; then
    echo ""
    echo -e "${GREEN}✅ AAB 已成功上傳到 Google Play Console！${NC}"
    echo -e "${YELLOW}發布軌道：${NC}$TRACK"
    echo -e "${YELLOW}檔案名稱：${NC}$AAB_NAME"
    echo -e "${YELLOW}版本：${NC}$VERSION ($BUILD_NUMBER)"
    echo -e "${YELLOW}建置時間：${NC}$BUILD_DATE"
    echo ""
    echo -e "${BLUE}💡 後續步驟：${NC}"
    echo "   1. 前往 Google Play Console 查看上傳狀態"
    echo "   2. 檢查應用程式審查狀態"
    echo "   3. 設定發布百分比（如適用）"
    echo "   4. 監控應用程式效能和錯誤報告"
else
    echo ""
    echo -e "${RED}❌ 上傳到 Google Play Console 失敗${NC}"
    echo -e "${YELLOW}請檢查：${NC}"
    echo "   1. 服務帳戶金鑰是否正確且有效"
    echo "   2. 應用程式是否已在 Google Play Console 中建立"
    echo "   3. 版本號是否大於目前發布的版本"
    echo "   4. 網路連線是否穩定"
    echo "   5. 簽名設定是否正確"
    echo ""
    echo -e "${BLUE}除錯指令：${NC}"
    echo "   ./gradlew publishReleaseBundle --info --stacktrace"
    cd ..
    exit 1
fi

cd ..

echo ""
echo -e "${GREEN}🎉 AAB 上傳流程完成！${NC}"
