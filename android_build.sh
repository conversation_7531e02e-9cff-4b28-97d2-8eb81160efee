#!/bin/bash
# Android 平台打包腳本
# 使用方式: ./android_build.sh [release|debug] [apk|aab|both] [--upload-play]
# 範例:
#   ./android_build.sh release apk                    # 只建置 APK
#   ./android_build.sh release aab                    # 只建置 AAB
#   ./android_build.sh release both                   # 建置 APK 和 AAB
#   ./android_build.sh release aab --upload-play      # 建置 AAB 並上傳到 Google Play Console

set -e  # 遇到錯誤時停止執行

# 顏色定義
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 設定 Flutter PATH
export PATH="/Users/<USER>/development/flutter/bin:$PATH"

# 參數解析
BUILD_TYPE="release"
BUILD_FORMAT="apk"
UPLOAD_TO_PLAY=false

# 解析第一個參數 (build type)
if [ $# -gt 0 ]; then
    case $1 in
        release|debug)
            BUILD_TYPE="$1"
            ;;
        *)
            echo -e "${RED}未知建置類型: $1${NC}"
            echo "使用方式: $0 [release|debug] [apk|aab|both] [--upload-play]"
            exit 1
            ;;
    esac
fi

# 解析第二個參數 (build format)
if [ $# -gt 1 ]; then
    case $2 in
        apk|aab|both)
            BUILD_FORMAT="$2"
            ;;
        --upload-play)
            UPLOAD_TO_PLAY=true
            ;;
        *)
            echo -e "${RED}未知建置格式: $2${NC}"
            echo "使用方式: $0 [release|debug] [apk|aab|both] [--upload-play]"
            exit 1
            ;;
    esac
fi

# 解析第三個參數 (upload option)
if [ $# -gt 2 ]; then
    case $3 in
        --upload-play)
            UPLOAD_TO_PLAY=true
            ;;
        *)
            echo -e "${RED}未知選項: $3${NC}"
            echo "使用方式: $0 [release|debug] [apk|aab|both] [--upload-play]"
            exit 1
            ;;
    esac
fi

echo -e "${BLUE}=== AstReal Android 打包腳本 ===${NC}"
echo -e "${YELLOW}建置類型：${NC}$BUILD_TYPE"
echo -e "${YELLOW}建置格式：${NC}$BUILD_FORMAT"
if [ "$UPLOAD_TO_PLAY" = true ]; then
    echo -e "${YELLOW}上傳選項：${NC}上傳到 Google Play Console"
fi
echo ""

# 防呆檢查
command -v flutter >/dev/null 2>&1 || { echo -e "${RED}❌ Flutter 未安裝${NC}"; exit 1; }
command -v firebase >/dev/null 2>&1 || { echo -e "${RED}❌ Firebase CLI 未安裝${NC}"; exit 1; }

# 檢查 Google Play Console 上傳所需工具
if [ "$UPLOAD_TO_PLAY" = true ]; then
    # 檢查是否有 Google Play Console 服務帳戶金鑰
    if [ ! -f "android/astreal-d3f70-firebase-adminsdk-fbsvc-8ae96f762a.json" ]; then
        echo -e "${RED}❌ 找不到 Google Play Console 服務帳戶金鑰檔案${NC}"
        echo -e "${YELLOW}請將服務帳戶 JSON 檔案放置於: android/astreal-d3f70-firebase-adminsdk-fbsvc-8ae96f762a.json${NC}"
        exit 1
    fi

    # 檢查 Gradle 是否支援 Play Publisher 插件
    if ! grep -q "com.github.triplet.play" android/app/build.gradle.kts; then
        echo -e "${YELLOW}⚠️  警告：未檢測到 Gradle Play Publisher 插件${NC}"
        echo -e "${YELLOW}建議在 android/app/build.gradle.kts 中新增該插件以支援自動上傳${NC}"
    fi
fi

# Firebase App Distribution 設定
APP_ID="1:470077449550:android:4971c9e15686127296aa1f"
TESTERS="<EMAIL>"

# 生成時間戳
TIMESTAMP=$(date +"%Y%m%d_%H%M%S")
COMPILATION_TIMESTAMP=$(date +"%Y%m%d%H%M%S")
BUILD_DATE=$(date +"%Y-%m-%d %H:%M:%S")

echo -e "${YELLOW}時間戳：${NC}$TIMESTAMP"
echo -e "${YELLOW}建置時間：${NC}$BUILD_DATE"
echo ""

# 從 pubspec.yaml 讀取版本號
VERSION=$(grep '^version:' pubspec.yaml | sed 's/version: //' | sed 's/+.*//')
if [ -z "$VERSION" ]; then
    echo -e "${RED}錯誤：無法從 pubspec.yaml 讀取版本號${NC}"
    exit 1
fi

# 設定建置號碼
# 從 pubspec.yaml 抓建置號碼（+ 後）
BUILD_NUMBER=$(grep '^version:' pubspec.yaml | sed 's/version: //' | cut -d'+' -f2)

# 設定建置號碼（Android 需要純數字且小於 2,100,000,000）
# 使用 Unix 時間戳（秒）作為建置號碼
# BUILD_NUMBER=$(date +%s)

echo -e "${YELLOW}應用版本：${NC}$VERSION"
echo -e "${YELLOW}建置號碼：${NC}$BUILD_NUMBER"
echo ""

# 取得最新 Git commit messages
echo -e "${BLUE}📝 取得 Git 提交記錄...${NC}"
GIT_MESSAGES=$(git log -30 --pretty=format:"- %s")

# 寫入到 RELEASE_NOTE 檔案
echo "Release Note:" > RELEASE_NOTE.txt
echo "$GIT_MESSAGES" >> RELEASE_NOTE.txt

# 建置函數
build_apk() {
    echo -e "${BLUE}🔨 建置 Android APK...${NC}"
    flutter build apk --$BUILD_TYPE \
      --build-name=$VERSION \
      --build-number=$BUILD_NUMBER \
      --dart-define=COMPILATION_TIMESTAMP=$COMPILATION_TIMESTAMP

    # 檢查 APK 是否存在
    if [ "$BUILD_TYPE" = "release" ]; then
        APK_PATH="build/app/outputs/flutter-apk/app-release.apk"
        APK_PREFIX="Astreal_release"
    else
        APK_PATH="build/app/outputs/flutter-apk/app-debug.apk"
        APK_PREFIX="Astreal_debug"
    fi

    if [ ! -f "$APK_PATH" ]; then
        echo -e "${RED}❌ 找不到 APK 檔案，建置可能失敗${NC}"
        exit 1
    fi

    # 重新命名 APK
    APK_NAME="${APK_PREFIX}_v${VERSION}_${BUILD_NUMBER}.apk"
    NEW_APK_PATH="build/app/outputs/flutter-apk/${APK_NAME}"
    mv "$APK_PATH" "$NEW_APK_PATH"

    echo -e "${YELLOW}APK 檔案：${NC}$APK_NAME"
}

build_aab() {
    echo -e "${BLUE}📦 建置 Android App Bundle (AAB)...${NC}"
    flutter build appbundle --$BUILD_TYPE \
      --build-name=$VERSION \
      --build-number=$BUILD_NUMBER \
      --dart-define=COMPILATION_TIMESTAMP=$COMPILATION_TIMESTAMP

    # 檢查 AAB 是否存在
    if [ "$BUILD_TYPE" = "release" ]; then
        AAB_PATH="build/app/outputs/bundle/release/app-release.aab"
        AAB_PREFIX="Astreal_release"
    else
        AAB_PATH="build/app/outputs/bundle/debug/app-debug.aab"
        AAB_PREFIX="Astreal_debug"
    fi

    if [ ! -f "$AAB_PATH" ]; then
        echo -e "${RED}❌ 找不到 AAB 檔案，建置可能失敗${NC}"
        exit 1
    fi

    # 重新命名 AAB
    AAB_NAME="${AAB_PREFIX}_v${VERSION}_${BUILD_NUMBER}.aab"
    NEW_AAB_PATH="build/app/outputs/bundle/${BUILD_TYPE}/${AAB_NAME}"
    mv "$AAB_PATH" "$NEW_AAB_PATH"

    echo -e "${YELLOW}AAB 檔案：${NC}$AAB_NAME"
}

# 根據建置格式執行相應的建置
case $BUILD_FORMAT in
    apk)
        build_apk
        ;;
    aab)
        build_aab
        ;;
    both)
        build_apk
        build_aab
        ;;
esac

# 上傳函數
upload_to_firebase() {
    local file_path=$1
    local file_name=$2

    echo -e "${BLUE}🚀 上傳到 Firebase App Distribution...${NC}"
    firebase appdistribution:distribute "$file_path" \
      --app "$APP_ID" \
      --release-notes "$GIT_MESSAGES" \
      --testers "$TESTERS"

    echo -e "${GREEN}✅ $file_name 已上傳到 Firebase App Distribution${NC}"
}

upload_to_play_console() {
    if [ "$BUILD_TYPE" != "release" ]; then
        echo -e "${RED}❌ 只有 release 版本可以上傳到 Google Play Console${NC}"
        return 1
    fi

    if [ ! -f "$NEW_AAB_PATH" ]; then
        echo -e "${RED}❌ 找不到 AAB 檔案，無法上傳到 Google Play Console${NC}"
        return 1
    fi

    echo -e "${BLUE}🚀 上傳 AAB 到 Google Play Console...${NC}"

    # 使用 Gradle Play Publisher 插件上傳
    cd android

    # 設定服務帳戶金鑰路徑
    export ANDROID_PUBLISHER_CREDENTIALS="play-console-service-account.json"

    # 上傳到內部測試軌道
    echo -e "${YELLOW}上傳到內部測試軌道...${NC}"
    ./gradlew publishReleaseBundle --track=internal

    if [ $? -eq 0 ]; then
        echo -e "${GREEN}✅ AAB 已成功上傳到 Google Play Console 內部測試軌道${NC}"
    else
        echo -e "${RED}❌ 上傳到 Google Play Console 失敗${NC}"
        echo -e "${YELLOW}請檢查：${NC}"
        echo "   1. 服務帳戶金鑰是否正確"
        echo "   2. 應用程式是否已在 Google Play Console 中建立"
        echo "   3. 版本號是否大於目前發布的版本"
        cd ..
        return 1
    fi

    cd ..
}

# 執行上傳
if [ "$BUILD_FORMAT" = "apk" ] || [ "$BUILD_FORMAT" = "both" ]; then
    upload_to_firebase "$NEW_APK_PATH" "$APK_NAME"
fi

if [ "$UPLOAD_TO_PLAY" = true ] && ([ "$BUILD_FORMAT" = "aab" ] || [ "$BUILD_FORMAT" = "both" ]); then
    upload_to_play_console
fi

# 總結報告
echo ""
echo -e "${GREEN}✅ Android 建置完成！${NC}"
echo -e "${YELLOW}建置時間：${NC}$BUILD_DATE"
echo -e "${YELLOW}版本：${NC}$VERSION ($BUILD_NUMBER)"
echo ""

if [ "$BUILD_FORMAT" = "apk" ] || [ "$BUILD_FORMAT" = "both" ]; then
    echo -e "${YELLOW}APK 檔案：${NC}$APK_NAME"
    echo -e "${YELLOW}APK 路徑：${NC}$NEW_APK_PATH"
fi

if [ "$BUILD_FORMAT" = "aab" ] || [ "$BUILD_FORMAT" = "both" ]; then
    echo -e "${YELLOW}AAB 檔案：${NC}$AAB_NAME"
    echo -e "${YELLOW}AAB 路徑：${NC}$NEW_AAB_PATH"
fi

echo ""
echo -e "${BLUE}💡 提示：${NC}"
if [ "$BUILD_FORMAT" = "apk" ] || [ "$BUILD_FORMAT" = "both" ]; then
    echo "   - 測試者將收到 Firebase App Distribution 的通知郵件"
    echo "   - 可以在 Firebase Console 中查看 APK 分發狀態"
fi

if [ "$UPLOAD_TO_PLAY" = true ] && ([ "$BUILD_FORMAT" = "aab" ] || [ "$BUILD_FORMAT" = "both" ]); then
    echo "   - AAB 已上傳到 Google Play Console 內部測試軌道"
    echo "   - 可以在 Google Play Console 中管理發布流程"
fi
