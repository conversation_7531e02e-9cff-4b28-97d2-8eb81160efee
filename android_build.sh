#!/bin/bash
# Android 平台打包腳本
# 使用方式: ./android_build.sh [release|debug]
# ./android_build.sh release

set -e  # 遇到錯誤時停止執行

# 顏色定義
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 設定 Flutter PATH
export PATH="/Users/<USER>/development/flutter/bin:$PATH"

# 參數解析
BUILD_TYPE="release"

if [ $# -gt 0 ]; then
    case $1 in
        release|debug)
            BUILD_TYPE="$1"
            ;;
        *)
            echo -e "${RED}未知參數: $1${NC}"
            echo "使用方式: $0 [release|debug]"
            exit 1
            ;;
    esac
fi

echo -e "${BLUE}=== AstReal Android 打包腳本 ===${NC}"
echo -e "${YELLOW}建置類型：${NC}$BUILD_TYPE"
echo ""

# 防呆檢查
command -v flutter >/dev/null 2>&1 || { echo -e "${RED}❌ Flutter 未安裝${NC}"; exit 1; }
command -v firebase >/dev/null 2>&1 || { echo -e "${RED}❌ Firebase CLI 未安裝${NC}"; exit 1; }

# Firebase App Distribution 設定
APP_ID="1:470077449550:android:4971c9e15686127296aa1f"
TESTERS="<EMAIL>"

# 生成時間戳
TIMESTAMP=$(date +"%Y%m%d_%H%M%S")
COMPILATION_TIMESTAMP=$(date +"%Y%m%d%H%M%S")
BUILD_DATE=$(date +"%Y-%m-%d %H:%M:%S")

echo -e "${YELLOW}時間戳：${NC}$TIMESTAMP"
echo -e "${YELLOW}建置時間：${NC}$BUILD_DATE"
echo ""

# 從 pubspec.yaml 讀取版本號
VERSION=$(grep '^version:' pubspec.yaml | sed 's/version: //' | sed 's/+.*//')
if [ -z "$VERSION" ]; then
    echo -e "${RED}錯誤：無法從 pubspec.yaml 讀取版本號${NC}"
    exit 1
fi

# 設定建置號碼
# 從 pubspec.yaml 抓建置號碼（+ 後）
BUILD_NUMBER=$(grep '^version:' pubspec.yaml | sed 's/version: //' | cut -d'+' -f2)

# 設定建置號碼（Android 需要純數字且小於 2,100,000,000）
# 使用 Unix 時間戳（秒）作為建置號碼
# BUILD_NUMBER=$(date +%s)

echo -e "${YELLOW}應用版本：${NC}$VERSION"
echo -e "${YELLOW}建置號碼：${NC}$BUILD_NUMBER"
echo ""

# 取得最新 Git commit messages
echo -e "${BLUE}📝 取得 Git 提交記錄...${NC}"
GIT_MESSAGES=$(git log -30 --pretty=format:"- %s")

# 寫入到 RELEASE_NOTE 檔案
echo "Release Note:" > RELEASE_NOTE.txt
echo "$GIT_MESSAGES" >> RELEASE_NOTE.txt

# 建置 APK
echo -e "${BLUE}🔨 建置 Android APK...${NC}"
flutter build apk --$BUILD_TYPE \
  --build-name=$VERSION \
  --build-number=$BUILD_NUMBER \
  --dart-define=COMPILATION_TIMESTAMP=$COMPILATION_TIMESTAMP

# 檢查 APK 是否存在
if [ "$BUILD_TYPE" = "release" ]; then
    APK_PATH="build/app/outputs/flutter-apk/app-release.apk"
    APK_PREFIX="Astreal_release"
else
    APK_PATH="build/app/outputs/flutter-apk/app-debug.apk"
    APK_PREFIX="Astreal_debug"
fi

if [ ! -f "$APK_PATH" ]; then
    echo -e "${RED}❌ 找不到 APK 檔案，建置可能失敗${NC}"
    exit 1
fi

# 重新命名 APK
APK_NAME="${APK_PREFIX}_v${VERSION}_${BUILD_NUMBER}.apk"
NEW_APK_PATH="build/app/outputs/flutter-apk/${APK_NAME}"
mv "$APK_PATH" "$NEW_APK_PATH"

echo -e "${YELLOW}APK 檔案：${NC}$APK_NAME"

# 上傳到 Firebase App Distribution
echo -e "${BLUE}🚀 上傳到 Firebase App Distribution...${NC}"
firebase appdistribution:distribute "$NEW_APK_PATH" \
  --app "$APP_ID" \
  --release-notes "$GIT_MESSAGES" \
  --testers "$TESTERS"

echo ""
echo -e "${GREEN}✅ Android APK 建置並上傳完成！${NC}"
echo -e "${YELLOW}檔案名稱：${NC}$APK_NAME"
echo -e "${YELLOW}建置時間：${NC}$BUILD_DATE"
echo -e "${YELLOW}版本：${NC}$VERSION ($BUILD_NUMBER)"
echo ""
echo -e "${BLUE}💡 提示：${NC}"
echo "   - 測試者將收到 Firebase App Distribution 的通知郵件"
echo "   - 可以在 Firebase Console 中查看分發狀態"
echo "   - APK 檔案已保存在: $NEW_APK_PATH"
